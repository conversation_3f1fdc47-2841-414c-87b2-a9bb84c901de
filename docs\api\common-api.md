# 通用接口说明

## 1. 文件上传接口

### 1.1 单文件上传
- **接口**: `POST /upload/single`
- **描述**: 上传单个文件
- **请求**: multipart/form-data
- **参数**: 
  - `file` (File): 上传的文件
  - `category` (string): 文件分类（avatar/service/order/banner等）
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "上传成功",
    "data": {
      "url": "https://example.com/uploads/file.jpg",
      "filename": "file.jpg",
      "size": 1024000,
      "mimeType": "image/jpeg"
    }
  }
  ```

### 1.2 多文件上传
- **接口**: `POST /upload/multiple`
- **描述**: 批量上传多个文件
- **请求**: multipart/form-data
- **参数**: 
  - `files` (File[]): 上传的文件数组
  - `category` (string): 文件分类
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "上传成功",
    "data": {
      "urls": [
        "https://example.com/uploads/file1.jpg",
        "https://example.com/uploads/file2.jpg"
      ],
      "successCount": 2,
      "failCount": 0
    }
  }
  ```

### 1.3 图片上传（压缩）
- **接口**: `POST /upload/image`
- **描述**: 上传图片并自动压缩
- **请求**: multipart/form-data
- **参数**: 
  - `image` (File): 上传的图片
  - `quality` (number): 压缩质量，1-100，默认80
  - `maxWidth` (number): 最大宽度，默认1920
  - `maxHeight` (number): 最大高度，默认1080

### 1.4 删除文件
- **接口**: `DELETE /upload/file`
- **描述**: 删除已上传的文件
- **请求体**: 
  ```json
  {
    "url": "https://example.com/uploads/file.jpg"
  }
  ```

### 1.5 获取上传配置
- **接口**: `GET /upload/config`
- **描述**: 获取文件上传配置信息
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "maxFileSize": 10485760,
      "allowedTypes": ["image/jpeg", "image/png", "image/gif"],
      "uploadPath": "/uploads/",
      "baseUrl": "https://example.com"
    }
  }
  ```

## 2. 位置服务接口

### 2.1 员工位置上报
- **接口**: `POST /openapi/location/updateEmployeeLocation`
- **描述**: 员工端定时上报当前位置
- **请求体**: 
  ```json
  {
    "employeeId": 1,
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "北京市朝阳区某某街道"
  }
  ```

### 2.2 查询员工位置
- **接口**: `GET /location/employee/{employeeId}`
- **描述**: 查询员工当前位置
- **参数**: `employeeId` (number): 员工ID
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "employeeId": 1,
      "latitude": 39.916527,
      "longitude": 116.397128,
      "address": "北京市朝阳区某某街道",
      "updateTime": "2024-01-01T10:00:00Z"
    }
  }
  ```

### 2.3 地址解析
- **接口**: `POST /location/geocode`
- **描述**: 根据地址获取经纬度
- **请求体**: 
  ```json
  {
    "address": "北京市朝阳区某某街道"
  }
  ```
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "latitude": 39.916527,
      "longitude": 116.397128,
      "formattedAddress": "北京市朝阳区某某街道"
    }
  }
  ```

### 2.4 逆地址解析
- **接口**: `POST /location/reverse-geocode`
- **描述**: 根据经纬度获取地址
- **请求体**: 
  ```json
  {
    "latitude": 39.916527,
    "longitude": 116.397128
  }
  ```
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "address": "北京市朝阳区某某街道",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "street": "某某街道"
    }
  }
  ```

### 2.5 计算距离
- **接口**: `POST /location/distance`
- **描述**: 计算两点之间的距离
- **请求体**: 
  ```json
  {
    "from": {
      "latitude": 39.916527,
      "longitude": 116.397128
    },
    "to": {
      "latitude": 39.926527,
      "longitude": 116.407128
    }
  }
  ```
- **响应**: 
  ```json
  {
    "errCode": 0,
    "msg": "OK",
    "data": {
      "distance": 1250.5,
      "unit": "meters"
    }
  }
  ```

## 3. 短信服务接口

### 3.1 发送验证码
- **接口**: `POST /sms/send-code`
- **描述**: 发送短信验证码
- **请求体**: 
  ```json
  {
    "phone": "13800138000",
    "type": "login"  // login/register/reset
  }
  ```

### 3.2 验证验证码
- **接口**: `POST /sms/verify-code`
- **描述**: 验证短信验证码
- **请求体**: 
  ```json
  {
    "phone": "13800138000",
    "code": "123456",
    "type": "login"
  }
  ```

### 3.3 发送通知短信
- **接口**: `POST /sms/send-notification`
- **描述**: 发送通知类短信
- **请求体**: 
  ```json
  {
    "phone": "13800138000",
    "template": "order_complete",
    "params": {
      "orderSn": "ORD20240101001",
      "serviceName": "宠物洗护"
    }
  }
  ```

## 4. 微信服务接口

### 4.1 微信登录
- **接口**: `POST /wechat/login`
- **描述**: 微信小程序登录
- **请求体**: 
  ```json
  {
    "code": "wx_code",
    "userInfo": {
      "nickName": "用户昵称",
      "avatarUrl": "头像URL"
    }
  }
  ```

### 4.2 微信支付
- **接口**: `POST /wechat/pay`
- **描述**: 发起微信支付
- **请求体**: 
  ```json
  {
    "orderSn": "ORD20240101001",
    "amount": 100.00,
    "description": "宠物洗护服务"
  }
  ```

### 4.3 支付回调
- **接口**: `POST /wechat/pay/callback`
- **描述**: 微信支付回调接口
- **说明**: 微信服务器调用，用于处理支付结果

### 4.4 查询支付状态
- **接口**: `GET /wechat/pay/status/{orderSn}`
- **描述**: 查询订单支付状态
- **参数**: `orderSn` (string): 订单号

### 4.5 申请退款
- **接口**: `POST /wechat/refund`
- **描述**: 申请微信退款
- **请求体**: 
  ```json
  {
    "orderSn": "ORD20240101001",
    "refundAmount": 100.00,
    "reason": "用户申请退款"
  }
  ```

### 4.6 退款回调
- **接口**: `POST /wechat/refund/callback`
- **描述**: 微信退款回调接口
- **说明**: 微信服务器调用，用于处理退款结果

## 5. 推送服务接口

### 5.1 发送推送消息
- **接口**: `POST /push/send`
- **描述**: 发送推送消息
- **请求体**: 
  ```json
  {
    "userIds": [1, 2, 3],
    "title": "订单状态更新",
    "content": "您的订单已完成",
    "type": "order",
    "data": {
      "orderId": 123
    }
  }
  ```

### 5.2 批量推送
- **接口**: `POST /push/batch`
- **描述**: 批量发送推送消息
- **请求体**: 
  ```json
  {
    "userType": "customer",  // customer/employee/all
    "title": "系统通知",
    "content": "系统将于今晚进行维护",
    "type": "system"
  }
  ```

### 5.3 设置推送配置
- **接口**: `PUT /push/config`
- **描述**: 设置用户推送配置
- **请求体**: 
  ```json
  {
    "userId": 1,
    "orderNotification": true,
    "systemNotification": true,
    "marketingNotification": false
  }
  ```

## 6. 缓存服务接口

### 6.1 设置缓存
- **接口**: `POST /cache/set`
- **描述**: 设置缓存数据
- **请求体**: 
  ```json
  {
    "key": "user_info_123",
    "value": {"id": 123, "name": "张三"},
    "ttl": 3600
  }
  ```

### 6.2 获取缓存
- **接口**: `GET /cache/get/{key}`
- **描述**: 获取缓存数据
- **参数**: `key` (string): 缓存键名

### 6.3 删除缓存
- **接口**: `DELETE /cache/delete/{key}`
- **描述**: 删除缓存数据
- **参数**: `key` (string): 缓存键名

### 6.4 清空缓存
- **接口**: `DELETE /cache/clear`
- **描述**: 清空所有缓存
- **参数**: 
  - `pattern` (string): 匹配模式，可选

## 7. 数据导出接口

### 7.1 导出订单数据
- **接口**: `POST /export/orders`
- **描述**: 导出订单数据为Excel
- **请求体**: 
  ```json
  {
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "status": ["已完成", "已评价"],
    "format": "xlsx"
  }
  ```

### 7.2 导出客户数据
- **接口**: `POST /export/customers`
- **描述**: 导出客户数据为Excel
- **请求体**: 
  ```json
  {
    "memberStatus": 1,
    "format": "xlsx"
  }
  ```

### 7.3 导出员工数据
- **接口**: `POST /export/employees`
- **描述**: 导出员工数据为Excel
- **请求体**: 
  ```json
  {
    "status": "在职",
    "format": "xlsx"
  }
  ```

### 7.4 查询导出任务状态
- **接口**: `GET /export/status/{taskId}`
- **描述**: 查询导出任务状态
- **参数**: `taskId` (string): 任务ID

### 7.5 下载导出文件
- **接口**: `GET /export/download/{taskId}`
- **描述**: 下载导出的文件
- **参数**: `taskId` (string): 任务ID

## 使用说明

### 文件上传规则
- 支持的图片格式：jpg, jpeg, png, gif
- 单个文件最大10MB
- 自动生成唯一文件名
- 支持图片压缩和尺寸调整

### 位置服务规则
- 员工位置信息静默处理，不抛出业务异常
- 支持地址和坐标的双向转换
- 距离计算使用球面距离公式

### 短信服务规则
- 验证码有效期5分钟
- 同一手机号1分钟内只能发送一次
- 支持多种短信模板

### 微信服务规则
- 支付金额单位为分
- 退款需要原路返回
- 回调接口需要验证签名

### 推送服务规则
- 支持个人和批量推送
- 用户可以设置推送偏好
- 推送消息支持自定义数据

### 缓存服务规则
- 默认TTL为1小时
- 支持模式匹配删除
- 缓存键名建议使用命名空间
