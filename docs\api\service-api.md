# 服务相关API接口

## 服务状态枚举
```typescript
enum ServiceStatus {
  enabled = 'enabled',   // 启用
  disabled = 'disabled'  // 禁用
}

enum ActivityTarget {
  用户端 = '用户端',
  员工端 = '员工端'
}
```

## 1. 服务管理接口

### 1.1 查询服务列表
- **接口**: `GET /service`
- **描述**: 管理端查询服务列表，支持分页和筛选
- **参数**: 
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `serviceName` (string): 服务名称筛选（模糊查询）
  - `filter` (string): 其他筛选条件

### 1.2 查询服务详情
- **接口**: `GET /service/{id}`
- **描述**: 查询指定服务的详细信息
- **参数**: `id` (number): 服务ID

### 1.3 创建服务
- **接口**: `POST /service`
- **描述**: 创建新服务
- **请求体**: 
  ```json
  {
    "serviceName": "宠物洗护",
    "logo": "https://example.com/logo.jpg",
    "description": "专业宠物洗护服务",
    "basePrice": 100.00,
    "serviceTypeId": 1,
    "published": true,
    "cardDiscountFlag": true,
    "avgDuration": 60
  }
  ```

### 1.4 更新服务
- **接口**: `PUT /service/{id}`
- **描述**: 更新服务信息
- **参数**: `id` (number): 服务ID
- **请求体**: 包含需要更新的字段

### 1.5 删除服务
- **接口**: `DELETE /service/{id}`
- **描述**: 删除服务
- **参数**: `id` (number): 服务ID

### 1.6 编辑服务
- **接口**: `POST /service/edit`
- **描述**: 批量编辑服务
- **参数**: 查询条件和更新数据

## 2. 服务类型接口

### 2.1 查询服务类型列表
- **接口**: `GET /openapi/service/types`
- **描述**: 查询服务类型列表（无需认证）
- **参数**: 
  - `type` (string): 类型筛选

### 2.2 获取指定类目下的服务列表
- **接口**: `GET /openapi/service/services/{typeId}`
- **描述**: 获取指定类目下的服务列表
- **参数**: 
  - `typeId` (number): 服务类型ID
  - `type` (string): 类型筛选
  - `hairType` (string): 毛发类型
  - `weightType` (string): 体重类型

## 3. 服务时长计算接口

### 3.1 更新所有服务平均时长
- **接口**: `POST /service-duration-calculator/update-all`
- **描述**: 手动更新所有服务的平均时长
- **参数**: 无

### 3.2 更新单个服务平均时长
- **接口**: `POST /service-duration-calculator/update/{serviceId}`
- **描述**: 手动更新单个服务的平均时长
- **参数**: `serviceId` (number): 服务ID

## 4. 照片墙管理接口

### 4.1 获取最新照片列表
- **接口**: `GET /photo-walls/latest/list`
- **描述**: 获取最新的照片墙展示列表（用户端）
- **参数**: 
  - `limit` (number): 返回数量限制，默认10

### 4.2 获取热门照片列表
- **接口**: `GET /photo-walls/popular/list`
- **描述**: 获取热门照片列表，按点赞数排序（用户端）
- **参数**: 
  - `limit` (number): 返回数量限制，默认10

### 4.3 查看照片详情
- **接口**: `GET /photo-walls/{id}`
- **描述**: 查看照片详情，自动增加浏览数
- **参数**: `id` (number): 照片ID

### 4.4 点赞照片
- **接口**: `POST /photo-walls/{id}/like`
- **描述**: 用户点赞照片
- **参数**: `id` (number): 照片ID

### 4.5 取消点赞
- **接口**: `DELETE /photo-walls/{id}/like`
- **描述**: 用户取消点赞照片
- **参数**: `id` (number): 照片ID

### 4.6 管理端查询照片列表
- **接口**: `GET /photo-walls`
- **描述**: 管理端查询照片墙列表，支持分页和筛选
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `isEnabled` (boolean): 启用状态筛选
  - `keyword` (string): 关键词搜索

### 4.7 新增照片墙记录
- **接口**: `POST /photo-walls`
- **描述**: 管理端手动新增照片墙记录
- **请求体**: 
  ```json
  {
    "orderId": 1,
    "employeeId": 1,
    "customerId": 1,
    "beforePhoto": "https://example.com/before1.jpg",
    "afterPhoto": "https://example.com/after1.jpg",
    "title": "小白的洗护服务",
    "description": "专业洗护，焕然一新",
    "serviceTypeName": "洗护服务",
    "petName": "小白",
    "petType": "狗",
    "priority": 10
  }
  ```

### 4.8 更新照片墙记录
- **接口**: `PUT /photo-walls/{id}`
- **描述**: 管理端更新照片墙记录
- **参数**: `id` (number): 照片ID
- **请求体**: 包含需要更新的字段

### 4.9 删除照片墙记录
- **接口**: `DELETE /photo-walls/{id}`
- **描述**: 管理端删除照片墙记录
- **参数**: `id` (number): 照片ID

### 4.10 启用/禁用照片
- **接口**: `PUT /photo-walls/{id}/toggle`
- **描述**: 管理端启用或禁用照片展示
- **参数**: `id` (number): 照片ID

### 4.11 设置照片优先级
- **接口**: `PUT /photo-walls/{id}/priority`
- **描述**: 管理端设置照片展示优先级
- **参数**: `id` (number): 照片ID
- **请求体**: 
  ```json
  {
    "priority": 10
  }
  ```

### 4.12 批量操作照片
- **接口**: `POST /photo-walls/batch`
- **描述**: 管理端批量操作照片（启用/禁用/删除）
- **请求体**: 
  ```json
  {
    "ids": [1, 2, 3],
    "action": "enable"  // enable/disable/delete
  }
  ```

## 5. 活动管理接口

### 5.1 查询活动列表
- **接口**: `GET /admin/activities`
- **描述**: 管理端查询活动列表
- **参数**: 
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `title` (string): 活动标题模糊查询
  - `target` (string): 受众筛选（用户端/员工端）
  - `isPublished` (number): 发布状态筛选

### 5.2 查询活动详情
- **接口**: `GET /admin/activities/{id}`
- **描述**: 管理端查询活动详情
- **参数**: `id` (number): 活动ID

### 5.3 创建活动
- **接口**: `POST /admin/activities`
- **描述**: 管理端创建新活动
- **请求体**: 
  ```json
  {
    "title": "春节活动",
    "contentType": "content",
    "content": "<p>活动详情...</p>",
    "url": "https://example.com/activity",
    "target": "用户端",
    "coverImage": "https://example.com/cover.jpg"
  }
  ```

### 5.4 更新活动
- **接口**: `PUT /admin/activities/{id}`
- **描述**: 管理端更新活动信息
- **参数**: `id` (number): 活动ID
- **请求体**: 包含需要更新的字段

### 5.5 删除活动
- **接口**: `DELETE /admin/activities/{id}`
- **描述**: 管理端删除活动（已发布的活动不能删除）
- **参数**: `id` (number): 活动ID

### 5.6 发布活动
- **接口**: `POST /admin/activities/{id}/publish`
- **描述**: 发布指定活动（同一受众同时只能有一个活动发布）
- **参数**: `id` (number): 活动ID

### 5.7 取消发布活动
- **接口**: `POST /admin/activities/{id}/unpublish`
- **描述**: 取消发布活动
- **参数**: `id` (number): 活动ID

### 5.8 查询当前发布的活动
- **接口**: `GET /admin/activities/published/current`
- **描述**: 管理端查询当前发布的活动
- **参数**: 
  - `target` (string): 受众，默认"用户端"

### 5.9 获取当前发布的活动（小程序端）
- **接口**: `GET /openapi/activities/current`
- **描述**: 小程序端获取当前发布的活动
- **参数**: 
  - `target` (string): 受众，默认"用户端"

### 5.10 获取活动详情（小程序端）
- **接口**: `GET /openapi/activities/{id}`
- **描述**: 小程序端获取活动详情
- **参数**: `id` (number): 活动ID

## 6. 服务照片接口

### 6.1 查询服务照片列表
- **接口**: `GET /service-photos`
- **描述**: 查询服务照片列表
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - 其他筛选条件

### 6.2 查询服务照片详情
- **接口**: `GET /service-photos/{id}`
- **描述**: 查询服务照片详情
- **参数**: `id` (number): 照片ID

### 6.3 创建服务照片记录
- **接口**: `POST /service-photos`
- **描述**: 创建服务照片记录
- **请求体**: 包含照片信息

### 6.4 删除服务照片
- **接口**: `DELETE /service-photos/{id}`
- **描述**: 删除服务照片
- **参数**: `id` (number): 照片ID

### 6.5 查询订单服务照片
- **接口**: `GET /service-photos/order/{orderId}`
- **描述**: 查询指定订单的服务照片
- **参数**: `orderId` (number): 订单ID

### 6.6 查询员工服务照片列表
- **接口**: `GET /service-photos/employee/{employeeId}`
- **描述**: 查询指定员工的服务照片列表
- **参数**: 
  - `employeeId` (number): 员工ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 6.7 设置服务前照片
- **接口**: `POST /service-photos/set-before-photos`
- **描述**: 设置服务前照片（完整替换）
- **请求体**: 包含照片URL数组

### 6.8 上传单张服务后照片
- **接口**: `POST /service-photos/upload-after`
- **描述**: 上传单张服务后照片
- **请求体**: 
  ```json
  {
    "orderId": 1,
    "employeeId": 1,
    "photoUrl": "https://example.com/photo.jpg"
  }
  ```

### 6.9 检查是否已上传服务前照片
- **接口**: `GET /service-photos/check/{orderId}/{employeeId}/before`
- **描述**: 检查是否已上传服务前照片
- **参数**: 
  - `orderId` (number): 订单ID
  - `employeeId` (number): 员工ID

### 6.10 检查是否已上传服务后照片
- **接口**: `GET /service-photos/check/{orderId}/{employeeId}/after`
- **描述**: 检查是否已上传服务后照片
- **参数**: 
  - `orderId` (number): 订单ID
  - `employeeId` (number): 员工ID

## 业务规则说明

### 服务管理规则
- 服务实体有启用/禁用字段，查询默认只显示启用的服务
- 现有业务数据始终显示关联的服务，不受状态影响

### 照片墙规则
- 支持最新和热门两种排序方式
- 用户可以点赞和取消点赞
- 管理员可以设置照片优先级和启用状态

### 活动管理规则
- 同一受众（用户端/员工端）同时只能有一个活动发布
- 发布新活动时会自动取消同受众的其他已发布活动
- 只有启用状态的活动才能发布
- 已发布的活动不能删除，需要先取消发布

### 服务照片规则
- 支持服务前后照片上传
- 每类最多9张照片
- 支持批量上传和单张上传
