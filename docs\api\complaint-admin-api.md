# 投诉建议管理API文档 - 管理端

## 概述

本文档描述了投诉建议管理模块的管理端API接口，包括投诉建议的录入、查询、处理、更新和删除等功能。

**基础路径**: `/admin/complaints`

## 数据模型

### 投诉建议对象 (Complaint)

```typescript
interface Complaint {
  id: number;                    // 投诉建议ID
  customerId?: number;           // 关联客户ID（可为空）
  orderId?: number;              // 关联订单ID
  employeeId?: number;           // 关联员工ID
  category: 'complaint' | 'suggestion';  // 大类：投诉/建议
  subCategory: 'order' | 'employee' | 'platform' | 'service' | 'workflow';  // 小类
  title: string;                 // 标题
  content: string;               // 内容
  contactInfo?: string;          // 联系方式
  photoURLs?: string[];          // 图片URL数组
  status: 'pending' | 'processing' | 'resolved' | 'closed';  // 状态
  result?: string;               // 处理结果
  handlerId?: number;            // 处理人员ID
  handledAt?: Date;              // 处理时间
  createdBy?: number;            // 录入人员ID（管理员录入时使用）
  adminNote?: string;            // 管理员录入备注
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
  
  // 关联对象
  customer?: {
    id: number;
    nickname: string;
    phone: string;
    avatar?: string;
  };
  order?: {
    id: number;
    sn: string;
    status: string;
    serviceTime?: Date;
  };
  employee?: {
    id: number;
    name: string;
    phone: string;
    avatar?: string;
  };
}
```

### 响应格式

```typescript
interface ApiResponse<T> {
  errCode: number;               // 错误码，0表示成功
  msg: string;                   // 响应消息
  data: T;                       // 响应数据
}

interface PaginationResponse<T> {
  list: T[];                     // 数据列表
  total: number;                 // 总数量
  page?: number;                 // 当前页码
  pageSize?: number;             // 每页数量
  totalPages?: number;           // 总页数
}
```

## API接口

### 1. 查询投诉建议列表

**接口**: `GET /admin/complaints/`

**描述**: 管理端查询所有投诉建议列表，支持分页和多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| customerId | number | 否 | 客户ID筛选 |
| orderId | number | 否 | 订单ID筛选 |
| employeeId | number | 否 | 员工ID筛选 |
| category | string | 否 | 大类筛选：complaint/suggestion |
| subCategory | string | 否 | 小类筛选：order/employee/platform/service/workflow |
| status | string | 否 | 状态筛选：pending/processing/resolved/closed |
| handlerId | number | 否 | 处理人员ID筛选 |
| keyword | string | 否 | 关键词搜索（标题和内容） |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "customerId": 123,
        "category": "complaint",
        "subCategory": "order",
        "title": "订单服务问题",
        "content": "服务质量不满意",
        "status": "pending",
        "createdAt": "2024-01-01T10:00:00Z",
        "customer": {
          "id": 123,
          "nickname": "张三",
          "phone": "13800138000"
        }
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10,
    "totalPages": 5
  }
}
```

### 2. 查询投诉建议详情

**接口**: `GET /admin/complaints/:id`

**描述**: 管理端按ID查询投诉建议详情

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | number | 是 | 投诉建议ID |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "customerId": 123,
    "orderId": 456,
    "employeeId": 789,
    "category": "complaint",
    "subCategory": "order",
    "title": "订单服务问题",
    "content": "服务质量不满意，希望改进",
    "contactInfo": "13800138000",
    "photoURLs": ["https://example.com/photo1.jpg"],
    "status": "pending",
    "createdBy": 1,
    "adminNote": "客户电话反馈",
    "createdAt": "2024-01-01T10:00:00Z",
    "customer": {
      "id": 123,
      "nickname": "张三",
      "phone": "13800138000",
      "avatar": "https://example.com/avatar.jpg"
    },
    "order": {
      "id": 456,
      "sn": "ORD20240101001",
      "status": "completed",
      "serviceTime": "2024-01-01T14:00:00Z"
    },
    "employee": {
      "id": 789,
      "name": "李四",
      "phone": "13900139000",
      "avatar": "https://example.com/emp_avatar.jpg"
    }
  }
}
```

### 3. 管理员录入投诉建议

**接口**: `POST /admin/complaints/`

**描述**: 管理端录入投诉建议，可以代表客户录入或录入不关联客户的投诉

**请求体**:
```json
{
  "category": "complaint",                    // 必填：complaint/suggestion
  "subCategory": "order",                     // 必填：order/employee/platform/service/workflow
  "title": "订单服务问题",                    // 必填：标题，最大200字符
  "content": "服务质量不满意，希望改进",       // 必填：内容，最大2000字符
  "customerId": 123,                          // 可选：客户ID
  "orderId": 456,                             // 可选：订单ID（订单投诉时必填）
  "employeeId": 789,                          // 可选：员工ID（人员投诉时必填）
  "contactInfo": "13800138000",               // 可选：联系方式，最大100字符
  "photoURLs": ["https://example.com/photo1.jpg"],  // 可选：图片URL数组，最多6张
  "createdBy": 1,                             // 必填：录入人员ID（管理员ID）
  "adminNote": "客户电话反馈"                 // 可选：管理员录入备注，最大500字符
}
```

**业务规则**:
- 订单投诉（subCategory=order）时，orderId必填
- 人员投诉（subCategory=employee）时，employeeId必填
- 订单投诉时，系统会自动从订单获取服务人员信息并关联
- customerId可为空，支持录入不关联客户的投诉

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "录入成功",
  "data": {
    "id": 1,
    "customerId": 123,
    "category": "complaint",
    "subCategory": "order",
    "title": "订单服务问题",
    "content": "服务质量不满意，希望改进",
    "status": "pending",
    "createdBy": 1,
    "adminNote": "客户电话反馈",
    "createdAt": "2024-01-01T10:00:00Z"
  }
}
```

### 4. 管理员更新投诉建议

**接口**: `PUT /admin/complaints/:id`

**描述**: 管理端更新投诉建议，管理员可以更新任何状态的投诉建议

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | number | 是 | 投诉建议ID |

**请求体**:
```json
{
  "category": "complaint",                    // 可选：complaint/suggestion
  "subCategory": "order",                     // 可选：order/employee/platform/service/workflow
  "title": "更新后的标题",                    // 可选：标题，最大200字符
  "content": "更新后的内容",                  // 可选：内容，最大2000字符
  "customerId": 123,                          // 可选：客户ID
  "orderId": 456,                             // 可选：订单ID
  "employeeId": 789,                          // 可选：员工ID
  "contactInfo": "13800138000",               // 可选：联系方式
  "photoURLs": ["https://example.com/photo1.jpg"],  // 可选：图片URL数组
  "adminNote": "管理员更新备注"               // 可选：管理员备注
}
```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "更新成功",
  "data": {
    "id": 1,
    "customerId": 123,
    "category": "complaint",
    "subCategory": "order",
    "title": "更新后的标题",
    "content": "更新后的内容",
    "status": "pending",
    "updatedAt": "2024-01-01T11:00:00Z"
  }
}
```

### 5. 处理投诉建议

**接口**: `PATCH /admin/complaints/:id/handle`

**描述**: 管理端处理投诉建议，更新处理状态和结果

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | number | 是 | 投诉建议ID |

**请求体**:
```json
{
  "status": "resolved",                       // 必填：processing/resolved/closed
  "result": "已联系客户并解决问题",           // 必填：处理结果，最大2000字符
  "handlerId": 1                              // 必填：处理人员ID
}
```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "处理成功",
  "data": {
    "id": 1,
    "status": "resolved",
    "result": "已联系客户并解决问题",
    "handlerId": 1,
    "handledAt": "2024-01-01T12:00:00Z"
  }
}
```

### 6. 更新投诉状态

**接口**: `PATCH /admin/complaints/:id/status`

**描述**: 管理端快速更新投诉建议状态

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | number | 是 | 投诉建议ID |

**请求体**:
```json
{
  "status": "processing"                      // 必填：pending/processing/resolved/closed
}
```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "状态更新成功",
  "data": {
    "id": 1,
    "status": "processing",
    "handledAt": "2024-01-01T12:00:00Z"
  }
}
```

### 7. 删除投诉建议

**接口**: `DELETE /admin/complaints/:id`

**描述**: 管理端删除投诉建议，管理员可以删除任何状态的投诉建议

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | number | 是 | 投诉建议ID |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "删除成功",
  "data": {
    "message": "删除成功"
  }
}
```

### 8. 批量处理投诉建议

**接口**: `POST /admin/complaints/batch-handle`

**描述**: 管理端批量处理投诉建议

**请求体**:
```json
{
  "ids": [1, 2, 3],                          // 必填：投诉建议ID数组，至少1个
  "handleData": {                             // 必填：处理数据
    "status": "resolved",                     // 必填：processing/resolved/closed
    "result": "批量处理完成",                 // 必填：处理结果
    "handlerId": 1                            // 必填：处理人员ID
  }
}
```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "批量处理完成",
  "data": {
    "message": "批量处理完成",
    "results": [
      {
        "id": 1,
        "success": true,
        "data": {
          "id": 1,
          "status": "resolved",
          "handledAt": "2024-01-01T12:00:00Z"
        }
      },
      {
        "id": 2,
        "success": false,
        "error": "投诉建议不存在"
      }
    ],
    "successCount": 1,
    "failCount": 1
  }
}
```

### 9. 批量删除投诉建议

**接口**: `DELETE /admin/complaints/batch`

**描述**: 管理端批量删除投诉建议

**请求体**:
```json
{
  "ids": [1, 2, 3]                           // 必填：投诉建议ID数组，至少1个
}
```

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "批量删除完成",
  "data": {
    "message": "批量删除完成",
    "results": [
      {
        "id": 1,
        "success": true
      },
      {
        "id": 2,
        "success": false,
        "error": "投诉建议不存在"
      }
    ],
    "successCount": 1,
    "failCount": 1
  }
}
```

### 10. 查询处理历史

**接口**: `GET /admin/complaints/:id/history`

**描述**: 管理端查询投诉建议处理历史

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | number | 是 | 投诉建议ID |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "complaint": {
      "id": 1,
      "title": "订单服务问题",
      "status": "resolved",
      "createdAt": "2024-01-01T10:00:00Z",
      "handledAt": "2024-01-01T12:00:00Z"
    },
    "history": [
      {
        "action": "创建",
        "timestamp": "2024-01-01T10:00:00Z",
        "operator": "管理员(1)",
        "details": "投诉建议已创建"
      },
      {
        "action": "处理",
        "timestamp": "2024-01-01T12:00:00Z",
        "operator": "管理员(1)",
        "details": "状态变更为: resolved"
      }
    ]
  }
}
```

### 11. 查询管理员录入的投诉

**接口**: `GET /admin/complaints/admin-created`

**描述**: 查询管理员录入的投诉建议列表

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| createdBy | number | 否 | 录入人员ID筛选 |
| keyword | string | 否 | 关键词搜索 |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |
| category | string | 否 | 大类筛选 |
| subCategory | string | 否 | 小类筛选 |
| status | string | 否 | 状态筛选 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "category": "complaint",
        "subCategory": "order",
        "title": "管理员录入的投诉",
        "status": "pending",
        "createdBy": 1,
        "adminNote": "客户电话反馈",
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 10
  }
}
```

### 12. 查询指定客户的投诉

**接口**: `GET /admin/complaints/customer/:customerId/complaints`

**描述**: 管理端查询指定客户的投诉建议

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "customerId": 123,
        "category": "complaint",
        "title": "客户投诉",
        "status": "pending",
        "createdAt": "2024-01-01T10:00:00Z",
        "order": {
          "id": 456,
          "sn": "ORD20240101001",
          "status": "completed"
        }
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 13. 查询指定订单的投诉

**接口**: `GET /admin/complaints/order/:orderId/complaints`

**描述**: 管理端查询指定订单的投诉建议

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 456,
        "category": "complaint",
        "subCategory": "order",
        "title": "订单相关投诉",
        "status": "pending",
        "createdAt": "2024-01-01T10:00:00Z",
        "customer": {
          "id": 123,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "order": {
          "id": 456,
          "sn": "ORD20240101001",
          "status": "completed"
        }
      }
    ],
    "total": 2
  }
}
```

### 14. 查询指定员工的投诉

**接口**: `GET /admin/complaints/employee/:employeeId/complaints`

**描述**: 管理端查询指定员工的投诉建议

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "list": [
      {
        "id": 1,
        "employeeId": 789,
        "category": "complaint",
        "subCategory": "employee",
        "title": "员工服务投诉",
        "status": "pending",
        "createdAt": "2024-01-01T10:00:00Z",
        "customer": {
          "id": 123,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "employee": {
          "id": 789,
          "name": "李四",
          "phone": "13900139000"
        }
      }
    ],
    "total": 3
  }
}
```

### 15. 获取投诉统计摘要

**接口**: `GET /admin/complaints/statistics/summary`

**描述**: 管理端获取投诉建议统计摘要

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| category | string | 否 | 大类筛选：complaint/suggestion |
| subCategory | string | 否 | 小类筛选：order/employee/platform/service/workflow |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "查询成功",
  "data": {
    "total": 100,
    "statusStats": [
      {
        "status": "pending",
        "count": 30
      },
      {
        "status": "processing",
        "count": 20
      },
      {
        "status": "resolved",
        "count": 40
      },
      {
        "status": "closed",
        "count": 10
      }
    ],
    "categoryStats": [
      {
        "category": "complaint",
        "count": 80
      },
      {
        "category": "suggestion",
        "count": 20
      }
    ],
    "subCategoryStats": [
      {
        "subCategory": "order",
        "count": 50
      },
      {
        "subCategory": "employee",
        "count": 30
      },
      {
        "subCategory": "platform",
        "count": 15
      },
      {
        "subCategory": "service",
        "count": 5
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数格式和必填项 |
| 404 | 资源不存在 | 检查ID是否正确，资源是否已被删除 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 常见错误示例

**参数验证错误**:
```json
{
  "errCode": 400,
  "msg": "大类必须是complaint或suggestion",
  "data": null
}
```

**资源不存在**:
```json
{
  "errCode": 404,
  "msg": "投诉建议不存在",
  "data": null
}
```

**业务逻辑错误**:
```json
{
  "errCode": 400,
  "msg": "订单投诉必须提供订单ID",
  "data": null
}
```

## 业务规则说明

### 投诉类型和子类型

| 大类 | 小类 | 描述 | 必填字段 |
|------|------|------|----------|
| complaint | order | 订单投诉 | orderId |
| complaint | employee | 人员投诉 | employeeId |
| complaint | platform | 平台投诉 | - |
| complaint | service | 服务投诉 | - |
| complaint | workflow | 流程投诉 | - |
| suggestion | platform | 平台建议 | - |
| suggestion | service | 服务建议 | - |
| suggestion | workflow | 流程建议 | - |

### 状态流转

```
pending (待处理) → processing (处理中) → resolved (已解决) / closed (已关闭)
```

- **pending**: 新创建的投诉建议，等待处理
- **processing**: 正在处理中
- **resolved**: 已解决
- **closed**: 已关闭（无需处理或无法解决）

### 权限说明

- 管理员拥有最高权限，可以操作任何状态的投诉建议
- 管理员可以代表客户录入投诉
- 管理员可以录入不关联客户的投诉（如匿名投诉）
- 管理员录入的投诉会有特殊标识（createdBy字段）

## 使用示例

### 1. 录入订单投诉

```javascript
// 1. 先录入投诉
const response = await fetch('/admin/complaints/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    category: 'complaint',
    subCategory: 'order',
    title: '订单服务质量问题',
    content: '客户反馈服务人员态度不好，服务质量差',
    customerId: 123,
    orderId: 456,
    contactInfo: '13800138000',
    createdBy: 1,
    adminNote: '客户电话投诉'
  })
});

// 2. 处理投诉
const handleResponse = await fetch('/admin/complaints/1/handle', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    status: 'resolved',
    result: '已联系客户道歉，并安排重新服务',
    handlerId: 1
  })
});
```

### 2. 批量处理投诉

```javascript
const batchResponse = await fetch('/admin/complaints/batch-handle', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    ids: [1, 2, 3],
    handleData: {
      status: 'resolved',
      result: '批量处理完成',
      handlerId: 1
    }
  })
});
```

### 3. 查询和筛选

```javascript
// 查询待处理的订单投诉
const queryResponse = await fetch('/admin/complaints/?status=pending&subCategory=order&current=1&pageSize=20');

// 查询指定客户的投诉历史
const customerComplaints = await fetch('/admin/complaints/customer/123/complaints');

// 获取统计数据
const stats = await fetch('/admin/complaints/statistics/summary?startDate=2024-01-01&endDate=2024-01-31');
```

## 注意事项

1. **数据关联**: 订单投诉时系统会自动关联订单的服务人员
2. **权限控制**: 管理员拥有最高权限，可以操作任何状态的投诉
3. **数据验证**: 所有接口都有完整的参数验证，请确保传入正确的数据格式
4. **图片上传**: photoURLs字段需要先通过文件上传接口获取URL
5. **分页查询**: 支持current/pageSize和offset/limit两种分页方式
6. **日期格式**: 所有日期参数使用YYYY-MM-DD格式
7. **批量操作**: 批量操作会返回每个操作的详细结果，包括成功和失败的统计

## 更新日志

- **v1.0.0**: 初始版本，包含基础的CRUD操作
- **v1.1.0**: 新增批量操作功能
- **v1.2.0**: 新增处理历史查询和状态快速更新功能
