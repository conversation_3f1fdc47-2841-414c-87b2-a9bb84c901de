# 客户相关API接口

## 客户状态枚举
```typescript
enum CustomerStatus {
  active = 'active',     // 活跃
  inactive = 'inactive'  // 非活跃
}

enum MemberStatus {
  普通会员 = 0,
  权益会员 = 1
}
```

## 1. 客户管理接口

### 1.1 查询客户列表
- **接口**: `GET /customers`
- **描述**: 管理端查询客户列表，支持分页和筛选
- **参数**: 
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `phone` (string): 手机号筛选（模糊查询）
  - `nickname` (string): 昵称筛选（模糊查询）

### 1.2 查询客户详情
- **接口**: `GET /customers/{id}`
- **描述**: 查询指定客户的详细信息
- **参数**: `id` (number): 客户ID

### 1.3 创建客户
- **接口**: `POST /customers`
- **描述**: 创建新客户
- **请求体**: 
  ```json
  {
    "nickname": "张三",
    "phone": "13800138000",
    "memberStatus": 0,
    "points": 0
  }
  ```

### 1.4 更新客户
- **接口**: `PUT /customers/{id}`
- **描述**: 更新客户信息
- **参数**: `id` (number): 客户ID
- **请求体**: 包含需要更新的字段

### 1.5 删除客户
- **接口**: `DELETE /customers/{id}`
- **描述**: 删除客户（软删除）
- **参数**: `id` (number): 客户ID

## 2. 宠物管理接口

### 2.1 获取客户宠物列表
- **接口**: `GET /customers/{id}/pets`
- **描述**: 获取指定客户的宠物列表
- **参数**: `id` (number): 客户ID

### 2.2 新增宠物
- **接口**: `POST /customers/{id}/pet`
- **描述**: 为客户新增宠物
- **参数**: `id` (number): 客户ID
- **请求体**: 
  ```json
  {
    "name": "小白",
    "type": "狗",
    "breed": "金毛",
    "age": 2,
    "weight": 25.5,
    "gender": "公"
  }
  ```

### 2.3 更新宠物
- **接口**: `PUT /customers/{id}/pet/{petId}`
- **描述**: 更新宠物信息
- **参数**: 
  - `id` (number): 客户ID
  - `petId` (number): 宠物ID
- **请求体**: 包含需要更新的宠物信息

### 2.4 删除宠物
- **接口**: `DELETE /customers/{id}/pet/{petId}`
- **描述**: 删除宠物
- **参数**: 
  - `id` (number): 客户ID
  - `petId` (number): 宠物ID

## 3. 地址管理接口

### 3.1 获取客户地址列表
- **接口**: `GET /customers/{id}/addresses`
- **描述**: 获取指定客户的地址列表
- **参数**: `id` (number): 客户ID

### 3.2 新增地址
- **接口**: `POST /customers/{id}/address`
- **描述**: 为客户新增地址
- **参数**: `id` (number): 客户ID
- **请求体**: 
  ```json
  {
    "address": "北京市朝阳区xxx",
    "longitude": 116.123456,
    "latitude": 39.123456,
    "addressDetail": "xxx小区1号楼",
    "addressRemark": "门口有保安",
    "isDefault": true
  }
  ```

### 3.3 更新地址
- **接口**: `PUT /customers/{id}/address/{addressId}`
- **描述**: 更新地址信息
- **参数**: 
  - `id` (number): 客户ID
  - `addressId` (number): 地址ID
- **请求体**: 包含需要更新的地址信息

### 3.4 删除地址
- **接口**: `DELETE /customers/{id}/address/{addressId}`
- **描述**: 删除地址
- **参数**: 
  - `id` (number): 客户ID
  - `addressId` (number): 地址ID

### 3.5 设置默认地址
- **接口**: `PUT /customers/{id}/address/{addressId}/default`
- **描述**: 设置默认地址
- **参数**: 
  - `id` (number): 客户ID
  - `addressId` (number): 地址ID

## 4. 权益卡管理接口

### 4.1 获取客户权益卡列表
- **接口**: `GET /customers/{customerId}/membership-cards`
- **描述**: 获取客户的权益卡列表
- **参数**: `customerId` (number): 客户ID

### 4.2 获取有效权益卡列表
- **接口**: `GET /customers/{customerId}/valid-membership-cards`
- **描述**: 获取客户的有效权益卡列表
- **参数**: `customerId` (number): 客户ID

### 4.3 获取可用权益卡
- **接口**: `GET /customers/{customerId}/available-membership-cards`
- **描述**: 获取可用的权益卡（用于下单时选择）
- **参数**: 
  - `customerId` (number): 客户ID
  - `serviceId` (number): 服务ID
  - `amount` (number): 订单金额

### 4.4 创建权益卡订单
- **接口**: `POST /customers/{customerId}/membership-card-order`
- **描述**: 为客户创建权益卡订单
- **参数**: `customerId` (number): 客户ID
- **请求体**: 
  ```json
  {
    "cardTypeId": 1,
    "remark": "购买备注"
  }
  ```

### 4.5 查询权益卡订单列表
- **接口**: `GET /membership-card-orders`
- **描述**: 查询客户的权益卡订单列表
- **参数**: 
  - `customerId` (number): 客户ID
  - `status` (string): 状态筛选，多个状态用逗号分隔
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 4.6 查询权益卡订单详情
- **接口**: `GET /membership-card-orders/{id}`
- **描述**: 查询权益卡订单详情
- **参数**: `id` (number): 订单ID

### 4.7 支付权益卡订单
- **接口**: `POST /membership-card-orders/{sn}/pay`
- **描述**: 支付权益卡订单
- **参数**: `sn` (string): 订单号
- **请求体**: 
  ```json
  {
    "customerId": 123
  }
  ```

## 5. 代金券管理接口

### 5.1 获取客户代金券列表
- **接口**: `GET /customers/{customerId}/coupons`
- **描述**: 获取客户的代金券列表
- **参数**: `customerId` (number): 客户ID

### 5.2 获取有效代金券列表
- **接口**: `GET /customers/{customerId}/valid-coupons`
- **描述**: 获取客户的有效代金券列表
- **参数**: `customerId` (number): 客户ID

### 5.3 获取可用代金券
- **接口**: `GET /customers/{customerId}/available-coupons`
- **描述**: 获取可用的代金券（用于下单时选择）
- **参数**: 
  - `customerId` (number): 客户ID
  - `serviceId` (number): 服务ID
  - `amount` (number): 订单金额

### 5.4 创建代金券订单
- **接口**: `POST /customers/{customerId}/coupon-order`
- **描述**: 为客户创建代金券订单
- **参数**: `customerId` (number): 客户ID
- **请求体**: 
  ```json
  {
    "couponId": 1,
    "remark": "购买备注"
  }
  ```

### 5.5 查询代金券订单列表
- **接口**: `GET /coupon-orders`
- **描述**: 查询客户的代金券订单列表
- **参数**: 
  - `customerId` (number): 客户ID
  - `status` (string): 状态筛选，多个状态用逗号分隔
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 5.6 查询代金券订单详情
- **接口**: `GET /coupon-orders/{id}`
- **描述**: 查询代金券订单详情
- **参数**: `id` (number): 订单ID

### 5.7 支付代金券订单
- **接口**: `POST /coupon-orders/{sn}/pay`
- **描述**: 支付代金券订单
- **参数**: `sn` (string): 订单号
- **请求体**: 
  ```json
  {
    "customerId": 123
  }
  ```

## 6. 客户推广接口

### 6.1 获取推广信息
- **接口**: `GET /customers/{customerId}/promotion`
- **描述**: 获取客户的推广信息
- **参数**: `customerId` (number): 客户ID

### 6.2 查询推广客户列表
- **接口**: `GET /customers/{customerId}/promoted-customers`
- **描述**: 查询客户推广的客户列表
- **参数**: 
  - `customerId` (number): 客户ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 6.3 设置推广关系
- **接口**: `POST /customers/{customerId}/set-promoter`
- **描述**: 设置客户的推广员
- **参数**: `customerId` (number): 客户ID
- **请求体**: 
  ```json
  {
    "promoterEmployeeId": 1
  }
  ```

## 7. 管理端客户操作接口

### 7.1 查询用户权益卡列表
- **接口**: `GET /customer-membership-cards`
- **描述**: 管理端查询用户权益卡列表
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - 其他筛选条件

### 7.2 新增用户权益卡
- **接口**: `POST /customer-membership-cards`
- **描述**: 管理端为用户新增权益卡
- **请求体**: 
  ```json
  {
    "customerId": 123,
    "cardTypeId": 1,
    "operatorId": 1
  }
  ```

### 7.3 查询用户代金券列表
- **接口**: `GET /customer-coupons`
- **描述**: 管理端查询用户代金券列表
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - 其他筛选条件

### 7.4 新增用户代金券
- **接口**: `POST /customer-coupons`
- **描述**: 管理端为用户新增代金券
- **请求体**: 
  ```json
  {
    "customerId": 123,
    "couponId": 1,
    "operatorId": 1
  }
  ```

## 业务规则说明

### 会员体系规则
- 用户注册后默认为普通会员
- 购买权益卡后变为权益会员
- 权益卡过期或使用次数用完后变回普通会员

### 权益卡规则
- 折扣卡必须填写折扣率
- 次卡必须填写可用次数且折扣强制为0
- 有效期为非必填字段

### 推广关系规则
- 员工-客户推广关系为一对多
- 一个员工可以推广多个客户
- 每个客户只能被一个员工推广
