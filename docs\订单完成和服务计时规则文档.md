# 订单完成和服务计时规则文档

## 概述

本文档详细说明了订单完成和服务计时的相关业务规则，包括主动完成订单、服务结束计时触发订单完成等场景的处理逻辑。

## 核心概念

### 服务类型
- **主服务**：订单的核心服务项目，必须完成
- **增项服务**：附加的服务项目，分为两种：
  - **主订单增项服务**：下单时选择的增项服务
  - **追加服务增项服务**：服务过程中临时申请的增项服务

### 计时状态
- **not_started**：未开始（没有计时记录或 startTime 为空）
- **in_progress**：进行中（有 startTime 但没有 endTime）
- **completed**：已完成（有 startTime 和 endTime）

## 订单完成触发场景

### 1. 主动完成订单
**接口**：`POST /orders/{orderId}/complete`
**触发条件**：员工手动点击完成订单

#### 处理流程
1. **状态检查**：订单必须处于"服务中"状态
2. **权限验证**：验证员工是否有权限操作此订单
3. **未付款增项检查**：检查是否存在未付款的附加服务
4. **服务计时处理**：统一处理所有需要时长统计的服务
5. **订单状态更新**：更新为"已完成"状态
6. **追加服务完成**：完成所有已支付的追加服务

#### 服务计时处理规则
- **主服务**：必然触发结束计时（有开始时间但没有结束时间的，立即结束）
- **增项服务**：只有已经开始的才会触发结束计时（没有开始时间的，跳过处理）

### 2. 服务结束计时触发订单完成
**接口**：`POST /employee/service-duration/end`
**触发条件**：员工结束单个服务项目计时后，系统自动检查

#### 自动完成检查条件（必须同时满足）
1. **所有主服务必须完成**
2. **不能存在未付款的附加服务**
3. **所有需要统计时长的增项服务必须完成**
4. **不能存在未开始但需要统计时长的增项服务**

## 详细业务规则

### 未付款增项服务检查
**未付款状态包括**：
- `PENDING_CONFIRM`：待确认
- `CONFIRMED`：已确认
- `PENDING_PAYMENT`：待付款

**检查逻辑**：
```typescript
// 通过订单详情获取所有附加服务订单
const orderDetails = await OrderDetail.findAll({
  where: { orderId },
  include: [{
    model: AdditionalServiceOrder,
    where: {
      status: [
        AdditionalServiceOrderStatus.PENDING_CONFIRM,
        AdditionalServiceOrderStatus.CONFIRMED,
        AdditionalServiceOrderStatus.PENDING_PAYMENT,
      ],
    },
    required: false,
  }],
});
```

### 主服务完成检查
**检查规则**：
- 每个主服务都必须有对应的完成记录
- 完成记录必须有结束时间（endTime 不为空）

**检查逻辑**：
```typescript
const completedRecord = await ServiceDurationRecord.findOne({
  where: {
    orderId,
    orderDetailId: orderDetail.id,
    serviceId: orderDetail.serviceId,
    recordType: ServiceDurationRecordType.MAIN_SERVICE,
    endTime: { [Op.not]: null },
  },
});
```

### 增项服务完成检查
**检查规则**：
1. 只检查 `needDurationTracking: true` 的增项服务
2. 必须有对应的服务记录
3. 服务记录必须有开始时间和结束时间

**检查逻辑**：
```typescript
// 查找该增项服务的时长记录
const serviceRecord = await ServiceDurationRecord.findOne({
  where: {
    orderId,
    additionalServiceOrderId: service.additionalServiceOrderId,
    additionalServiceId: service.additionalServiceId,
    recordType: ServiceDurationRecordType.ADDITIONAL_SERVICE,
  },
});

if (!serviceRecord) {
  // 没有时长记录，说明还未开始
  return false;
}

if (!serviceRecord.endTime) {
  // 有记录但未结束，说明还在进行中
  return false;
}
```

## 服务计时管理

### 开始服务计时
**接口**：`POST /employee/service-duration/start`

#### 验证规则
1. **订单状态**：必须为"服务中"
2. **权限验证**：员工必须是订单的负责人
3. **重复检查**：防止重复开始相同的服务
4. **增项服务特殊规则**：
   - 只有 `needDurationTracking: true` 的增项服务才能开始计时
   - 追加服务必须是已支付状态

#### 重复开始检查逻辑
```typescript
// 检查是否已有未结束的完全相同的服务记录
const existingRecord = await ServiceDurationRecord.findOne({
  where: {
    orderId,
    employeeId,
    recordType,
    endTime: null, // 查找未结束的记录
    // 根据服务类型添加具体的唯一标识条件
  },
});

if (existingRecord) {
  if (existingRecord.startTime) {
    // 真正已开始的服务，不能重复开始
    throw new CustomError('服务已开始，请先结束当前服务再重新开始');
  } else {
    // 无效记录（startTime为null），删除并重新创建
    await existingRecord.destroy();
  }
}
```

### 结束服务计时
**接口**：`POST /employee/service-duration/end`

#### 验证规则
1. **记录存在性**：服务记录必须存在
2. **权限验证**：员工必须是记录的创建者
3. **状态检查**：服务不能已经结束
4. **开始检查**：服务必须已经开始（有 startTime）

#### 自动触发订单完成
结束服务后，系统会自动检查是否满足订单完成条件，如果满足则自动完成订单。

## 订单整体服务管理

### 开始整体订单服务
**接口**：`POST /employee/service-duration/start-order-service`

#### 处理逻辑
1. **订单状态更新**：从"已出发"或"待服务"更新为"服务中"
2. **记录开始时间**：记录实际服务开始时间
3. **自动开始主服务**：为所有主服务创建计时记录并开始计时
4. **上传服务前照片**：可选功能

#### 注意事项
- 增项服务需要员工手动开始
- 只有 `needDurationTracking: true` 的增项服务才需要计时

## 状态查询接口

### 查询增项服务状态
**接口**：`GET /order-details/{orderDetailId}/additional-services`

#### 返回字段说明
- **durationStatus**：计时状态（not_started/in_progress/completed）
- **recordId**：计时记录ID（用于调用结束接口）
- **needDurationTracking**：是否需要统计时长

#### 状态判断逻辑
```typescript
if (service.needDurationTracking && service.serviceDurationRecords?.length > 0) {
  const record = service.serviceDurationRecords[0];
  recordId = record.id;
  if (record.startTime && record.endTime) {
    durationStatus = 'completed';
  } else if (record.startTime) {
    durationStatus = 'in_progress';
  }
}
```

## 异常处理

### 常见异常情况
1. **服务尚未开始**：调用结束接口时，服务记录没有 startTime
2. **服务已结束**：重复调用结束接口
3. **权限不足**：员工操作不属于自己的订单或服务
4. **状态不正确**：订单不在正确的状态下进行操作

### 数据一致性保障
1. **事务处理**：复杂操作使用数据库事务
2. **状态检查**：操作前严格检查各种状态
3. **异步处理**：非关键操作（如平均时长更新）使用异步处理
4. **错误恢复**：无效记录自动清理机制

## 相关文件

### 主要服务文件
- `src/service/order.service.ts`：订单管理服务
- `src/service/service-duration-record.service.ts`：服务计时记录服务
- `src/service/order-duration-calculator.service.ts`：订单时长计算服务

### 主要控制器文件
- `src/controller/order.controller.ts`：订单操作接口
- `src/controller/employee/service-duration.controller.ts`：员工端服务计时接口
- `src/controller/additional-service-order.controller.ts`：增项服务管理接口

### 实体文件
- `src/entity/service-duration-record.entity.ts`：服务时长记录实体
- `src/entity/order.entity.ts`：订单实体
- `src/entity/additional-service-order.entity.ts`：追加服务订单实体
