# 投诉建议管理 - 前端开发指南

## 概述

本文档为前端开发人员提供投诉建议管理模块的开发指南，包括页面设计建议、状态管理、表单验证等内容。

## 页面结构建议

### 1. 投诉建议列表页面

**路由**: `/admin/complaints`

**功能模块**:
- 搜索筛选区域
- 数据表格
- 批量操作工具栏
- 分页组件

**关键功能**:
```typescript
// 列表查询
interface QueryParams {
  current: number;
  pageSize: number;
  status?: string;
  category?: string;
  subCategory?: string;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  customerId?: number;
  orderId?: number;
  employeeId?: number;
  handlerId?: number;
  createdBy?: number;
}

// 表格列配置建议
const columns = [
  { title: 'ID', dataIndex: 'id', width: 80 },
  { title: '类型', dataIndex: 'category', width: 100 },
  { title: '子类型', dataIndex: 'subCategory', width: 120 },
  { title: '标题', dataIndex: 'title', width: 200 },
  { title: '状态', dataIndex: 'status', width: 100 },
  { title: '客户', dataIndex: ['customer', 'nickname'], width: 120 },
  { title: '录入人', dataIndex: 'createdBy', width: 100 },
  { title: '创建时间', dataIndex: 'createdAt', width: 150 },
  { title: '操作', width: 200 }
];
```

### 2. 投诉建议详情页面

**路由**: `/admin/complaints/:id`

**功能模块**:
- 基本信息展示
- 关联信息（客户、订单、员工）
- 处理历史
- 操作按钮区域

### 3. 录入投诉建议页面

**路由**: `/admin/complaints/create`

**表单字段**:
```typescript
interface CreateForm {
  category: 'complaint' | 'suggestion';
  subCategory: 'order' | 'employee' | 'platform' | 'service' | 'workflow';
  title: string;
  content: string;
  customerId?: number;
  orderId?: number;
  employeeId?: number;
  contactInfo?: string;
  photoURLs?: string[];
  adminNote?: string;
}
```

## 状态管理建议

### 1. 状态定义

```typescript
// 投诉状态枚举
enum ComplaintStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

// 状态显示配置
const statusConfig = {
  pending: { text: '待处理', color: 'orange' },
  processing: { text: '处理中', color: 'blue' },
  resolved: { text: '已解决', color: 'green' },
  closed: { text: '已关闭', color: 'gray' }
};

// 类型配置
const categoryConfig = {
  complaint: { text: '投诉', color: 'red' },
  suggestion: { text: '建议', color: 'blue' }
};

const subCategoryConfig = {
  order: '订单',
  employee: '人员',
  platform: '平台',
  service: '服务',
  workflow: '流程'
};
```

### 2. API 调用封装

```typescript
// API 服务封装
class ComplaintAdminService {
  private baseUrl = '/admin/complaints';

  // 查询列表
  async getList(params: QueryParams) {
    return await request.get(`${this.baseUrl}/`, { params });
  }

  // 查询详情
  async getDetail(id: number) {
    return await request.get(`${this.baseUrl}/${id}`);
  }

  // 录入投诉
  async create(data: CreateForm) {
    return await request.post(`${this.baseUrl}/`, data);
  }

  // 更新投诉
  async update(id: number, data: Partial<CreateForm>) {
    return await request.put(`${this.baseUrl}/${id}`, data);
  }

  // 处理投诉
  async handle(id: number, data: HandleData) {
    return await request.patch(`${this.baseUrl}/${id}/handle`, data);
  }

  // 更新状态
  async updateStatus(id: number, status: string) {
    return await request.patch(`${this.baseUrl}/${id}/status`, { status });
  }

  // 删除投诉
  async delete(id: number) {
    return await request.delete(`${this.baseUrl}/${id}`);
  }

  // 批量处理
  async batchHandle(ids: number[], handleData: HandleData) {
    return await request.post(`${this.baseUrl}/batch-handle`, { ids, handleData });
  }

  // 批量删除
  async batchDelete(ids: number[]) {
    return await request.delete(`${this.baseUrl}/batch`, { data: { ids } });
  }

  // 获取统计
  async getStatistics(params?: any) {
    return await request.get(`${this.baseUrl}/statistics/summary`, { params });
  }

  // 查询处理历史
  async getHistory(id: number) {
    return await request.get(`${this.baseUrl}/${id}/history`);
  }
}
```

## 表单验证规则

### 1. 录入表单验证

```typescript
const createFormRules = {
  category: [
    { required: true, message: '请选择投诉类型' }
  ],
  subCategory: [
    { required: true, message: '请选择子类型' }
  ],
  title: [
    { required: true, message: '请输入标题' },
    { max: 200, message: '标题不能超过200字符' }
  ],
  content: [
    { required: true, message: '请输入内容' },
    { max: 2000, message: '内容不能超过2000字符' }
  ],
  orderId: [
    { 
      validator: (rule, value, callback) => {
        const { subCategory } = form.getFieldsValue();
        if (subCategory === 'order' && !value) {
          callback('订单投诉必须选择订单');
        } else {
          callback();
        }
      }
    }
  ],
  employeeId: [
    { 
      validator: (rule, value, callback) => {
        const { subCategory } = form.getFieldsValue();
        if (subCategory === 'employee' && !value) {
          callback('人员投诉必须选择员工');
        } else {
          callback();
        }
      }
    }
  ],
  contactInfo: [
    { max: 100, message: '联系方式不能超过100字符' }
  ],
  adminNote: [
    { max: 500, message: '备注不能超过500字符' }
  ]
};
```

### 2. 处理表单验证

```typescript
const handleFormRules = {
  status: [
    { required: true, message: '请选择处理状态' }
  ],
  result: [
    { required: true, message: '请输入处理结果' },
    { max: 2000, message: '处理结果不能超过2000字符' }
  ],
  handlerId: [
    { required: true, message: '请选择处理人员' }
  ]
};
```

## 组件设计建议

### 1. 状态标签组件

```typescript
interface StatusTagProps {
  status: string;
  size?: 'small' | 'default';
}

const StatusTag: React.FC<StatusTagProps> = ({ status, size = 'default' }) => {
  const config = statusConfig[status] || { text: status, color: 'default' };
  return <Tag color={config.color} size={size}>{config.text}</Tag>;
};
```

### 2. 类型标签组件

```typescript
interface CategoryTagProps {
  category: string;
  subCategory?: string;
}

const CategoryTag: React.FC<CategoryTagProps> = ({ category, subCategory }) => {
  const categoryText = categoryConfig[category]?.text || category;
  const subCategoryText = subCategory ? subCategoryConfig[subCategory] : '';
  
  return (
    <Space>
      <Tag color={categoryConfig[category]?.color}>{categoryText}</Tag>
      {subCategoryText && <Tag>{subCategoryText}</Tag>}
    </Space>
  );
};
```

### 3. 批量操作组件

```typescript
interface BatchOperationProps {
  selectedRowKeys: number[];
  onBatchHandle: (handleData: HandleData) => void;
  onBatchDelete: () => void;
  loading?: boolean;
}

const BatchOperation: React.FC<BatchOperationProps> = ({
  selectedRowKeys,
  onBatchHandle,
  onBatchDelete,
  loading
}) => {
  const [handleModalVisible, setHandleModalVisible] = useState(false);
  
  return (
    <Space>
      <span>已选择 {selectedRowKeys.length} 项</span>
      <Button 
        disabled={selectedRowKeys.length === 0}
        onClick={() => setHandleModalVisible(true)}
      >
        批量处理
      </Button>
      <Button 
        danger
        disabled={selectedRowKeys.length === 0}
        onClick={onBatchDelete}
        loading={loading}
      >
        批量删除
      </Button>
    </Space>
  );
};
```

## 权限控制

```typescript
// 权限检查
const hasPermission = (action: string) => {
  const permissions = getCurrentUserPermissions();
  return permissions.includes(`complaint:${action}`);
};

// 操作按钮权限控制
const ActionButtons = ({ record }) => (
  <Space>
    <Button size="small" onClick={() => viewDetail(record.id)}>
      查看
    </Button>
    {hasPermission('update') && (
      <Button size="small" onClick={() => editComplaint(record.id)}>
        编辑
      </Button>
    )}
    {hasPermission('handle') && (
      <Button size="small" onClick={() => handleComplaint(record.id)}>
        处理
      </Button>
    )}
    {hasPermission('delete') && (
      <Button size="small" danger onClick={() => deleteComplaint(record.id)}>
        删除
      </Button>
    )}
  </Space>
);
```

## 错误处理

```typescript
// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response) {
    const { errCode, msg } = error.response.data;
    switch (errCode) {
      case 400:
        message.error(`参数错误: ${msg}`);
        break;
      case 404:
        message.error('资源不存在');
        break;
      case 500:
        message.error('服务器错误，请稍后重试');
        break;
      default:
        message.error(msg || '操作失败');
    }
  } else {
    message.error('网络错误，请检查网络连接');
  }
};

// 在API调用中使用
try {
  const result = await complaintService.create(formData);
  message.success('录入成功');
  // 处理成功逻辑
} catch (error) {
  handleApiError(error);
}
```

## 性能优化建议

1. **列表分页**: 使用服务端分页，避免一次性加载大量数据
2. **搜索防抖**: 对搜索输入进行防抖处理
3. **图片懒加载**: 投诉图片使用懒加载
4. **缓存策略**: 对不经常变化的数据（如员工列表）进行缓存
5. **虚拟滚动**: 大数据量时考虑使用虚拟滚动

## 测试建议

1. **单元测试**: 对API服务、工具函数进行单元测试
2. **集成测试**: 测试完整的业务流程
3. **E2E测试**: 测试关键用户路径

## 部署注意事项

1. **环境变量**: 配置不同环境的API地址
2. **路由配置**: 确保前端路由与后端API路径匹配
3. **权限配置**: 根据用户角色显示不同的功能模块
