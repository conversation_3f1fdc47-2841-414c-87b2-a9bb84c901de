# 投诉建议管理API文档

## 文档概述

本目录包含投诉建议管理模块的完整API文档，为前端开发团队提供详细的接口说明和开发指南。

## 文档结构

### 📋 API接口文档

| 文件名 | 描述 | 适用对象 |
|--------|------|----------|
| [complaint-admin-api.md](./complaint-admin-api.md) | 完整的API接口文档 | 后端开发、前端开发 |
| [complaint-admin-api-summary.md](./complaint-admin-api-summary.md) | API接口总览（简化版） | 前端开发、产品经理 |

### 🛠️ 开发工具

| 文件名 | 描述 | 使用方法 |
|--------|------|----------|
| [complaint-admin-postman.json](./complaint-admin-postman.json) | Postman接口测试集合 | 导入Postman进行接口测试 |
| [complaint-admin-frontend-guide.md](./complaint-admin-frontend-guide.md) | 前端开发指南 | 前端开发参考 |

## 快速开始

### 1. 后端开发者

1. 查看 [完整API文档](./complaint-admin-api.md) 了解所有接口详情
2. 使用 [Postman集合](./complaint-admin-postman.json) 进行接口测试
3. 参考业务规则和错误处理说明

### 2. 前端开发者

1. 先阅读 [API总览](./complaint-admin-api-summary.md) 快速了解接口
2. 查看 [前端开发指南](./complaint-admin-frontend-guide.md) 获取开发建议
3. 使用 [Postman集合](./complaint-admin-postman.json) 测试接口
4. 需要详细信息时参考 [完整API文档](./complaint-admin-api.md)

### 3. 产品经理/测试人员

1. 查看 [API总览](./complaint-admin-api-summary.md) 了解功能范围
2. 使用 [Postman集合](./complaint-admin-postman.json) 进行功能测试

## 核心功能

### 🔍 查询功能
- ✅ 投诉建议列表查询（支持多条件筛选和分页）
- ✅ 投诉建议详情查询
- ✅ 按客户/订单/员工查询投诉
- ✅ 管理员录入的投诉查询
- ✅ 投诉统计摘要
- ✅ 处理历史查询

### ✏️ 操作功能
- ✅ 管理员录入投诉建议
- ✅ 管理员更新投诉建议
- ✅ 处理投诉建议
- ✅ 快速更新投诉状态
- ✅ 删除投诉建议

### 📦 批量操作
- ✅ 批量处理投诉建议
- ✅ 批量删除投诉建议

## 接口总览

| 分类 | 接口数量 | 主要功能 |
|------|----------|----------|
| 查询类 | 8个 | 列表查询、详情查询、统计查询 |
| 操作类 | 5个 | 增删改、处理、状态更新 |
| 批量操作 | 2个 | 批量处理、批量删除 |
| **总计** | **15个** | **完整的CRUD和业务操作** |

## 技术特性

### 🔐 权限控制
- 管理员拥有最高权限
- 可以操作任何状态的投诉建议
- 支持权限验证和角色控制

### 📊 数据验证
- 完整的DTO参数验证
- 业务规则验证
- 关联数据完整性检查

### 🔄 状态管理
- 完整的状态流转控制
- 处理历史记录
- 自动时间戳管理

### 🎯 业务规则
- 订单投诉自动关联服务人员
- 支持录入不关联客户的投诉
- 管理员录入特殊标识

## 数据模型

### 核心实体
```typescript
interface Complaint {
  id: number;                    // 主键
  customerId?: number;           // 客户ID（可为空）
  orderId?: number;              // 订单ID
  employeeId?: number;           // 员工ID
  category: string;              // 大类：complaint/suggestion
  subCategory: string;           // 小类：order/employee/platform/service/workflow
  title: string;                 // 标题
  content: string;               // 内容
  status: string;                // 状态：pending/processing/resolved/closed
  createdBy?: number;            // 录入人员ID（管理员标识）
  adminNote?: string;            // 管理员备注
  // ... 其他字段
}
```

### 关联关系
- **Customer**: 客户信息（可选关联）
- **Order**: 订单信息（订单投诉时关联）
- **Employee**: 员工信息（人员投诉或服务人员）

## 使用Postman测试

### 1. 导入集合
1. 打开Postman
2. 点击 Import
3. 选择 [complaint-admin-postman.json](./complaint-admin-postman.json)
4. 导入成功

### 2. 配置环境变量
```json
{
  "baseUrl": "http://localhost:7001",
  "token": "your_admin_token_here"
}
```

### 3. 测试流程
1. 先测试查询接口确认服务正常
2. 测试录入功能
3. 测试处理功能
4. 测试批量操作

## 常见问题

### Q1: 订单投诉时员工ID如何处理？
A: 系统会自动从订单信息中获取服务人员ID并关联，无需手动指定。

### Q2: 管理员可以删除任何状态的投诉吗？
A: 是的，管理员拥有最高权限，可以删除任何状态的投诉建议。

### Q3: 如何区分管理员录入的投诉？
A: 通过 `createdBy` 字段，如果有值则表示是管理员录入的。

### Q4: 批量操作失败怎么处理？
A: 批量操作会返回每个操作的详细结果，包括成功和失败的统计，可以根据结果进行后续处理。

### Q5: 图片上传如何处理？
A: 需要先通过文件上传接口获取图片URL，然后在 `photoURLs` 字段中传入URL数组。

## 更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2024-01-01 | 初始版本，基础CRUD功能 |
| v1.1.0 | 2024-01-02 | 新增批量操作功能 |
| v1.2.0 | 2024-01-03 | 新增处理历史和状态快速更新 |

## 联系方式

如有问题或建议，请联系：
- 后端开发团队：<EMAIL>
- 前端开发团队：<EMAIL>
- 产品团队：<EMAIL>

---

**注意**: 请确保在生产环境中使用正确的API地址和认证token。
