# API接口文档总览

## 概述

本目录包含宠物服务管理系统的所有API接口文档，按功能模块分类组织。所有接口文档都采用精简格式，只包含核心的接口定义信息。

## 统一返回格式

### 成功响应
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
```json
{
  "errCode": 400,
  "msg": "错误信息"
}
```

## 认证说明

所有API接口都需要在请求头中携带认证信息：
```
Authorization: Bearer <token>
```

## 分页参数

支持分页的接口统一使用以下参数：
- `page`: 页码，从1开始
- `pageSize`: 每页数量，默认10，最大100

## 文档结构

### 核心业务模块
- [订单相关API](./order-api.md) - 订单管理、追加服务、服务计时等
- [客户相关API](./customer-api.md) - 客户管理、会员权益等
- [员工相关API](./employee-api.md) - 员工管理、出车拍照、职位权限等
- [服务相关API](./service-api.md) - 服务管理、照片墙、活动管理等

### 支持功能模块
- [投诉建议API](./complaint-api.md) - 投诉建议管理
- [系统管理API](./system-api.md) - 权限管理、轮播图等
- [通用接口说明](./common-api.md) - 文件上传、位置服务等

## 接口命名规范

### 路径规范
- 用户端接口：`/api/user/*`
- 员工端接口：`/api/employee/*`
- 管理端接口：`/api/admin/*`

### HTTP方法
- `GET` - 查询数据
- `POST` - 创建数据
- `PUT` - 更新数据（完整更新）
- `PATCH` - 更新数据（部分更新）
- `DELETE` - 删除数据

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
