# 投诉建议API接口

## 投诉建议状态枚举
```typescript
enum ComplaintStatus {
  pending = 'pending',      // 待处理
  processing = 'processing', // 处理中
  resolved = 'resolved',    // 已解决
  closed = 'closed'         // 已关闭
}

enum ComplaintCategory {
  complaint = 'complaint',   // 投诉
  suggestion = 'suggestion'  // 建议
}

enum ComplaintSubCategory {
  order = 'order',          // 订单
  employee = 'employee',    // 员工
  platform = 'platform',   // 平台
  service = 'service',      // 服务
  workflow = 'workflow'     // 流程
}
```

## 1. 查询接口

### 1.1 查询投诉建议列表
- **接口**: `GET /admin/complaints`
- **描述**: 管理端查询投诉建议列表，支持多条件筛选和分页
- **参数**: 
  - `page` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `category` (string): 大类筛选 (complaint/suggestion)
  - `subCategory` (string): 小类筛选
  - `status` (string): 状态筛选
  - `customerId` (number): 客户ID筛选
  - `employeeId` (number): 员工ID筛选
  - `handlerId` (number): 处理人员ID筛选
  - `keyword` (string): 关键词搜索（标题和内容）
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 1.2 查询投诉建议详情
- **接口**: `GET /admin/complaints/{id}`
- **描述**: 查询指定投诉建议的详细信息
- **参数**: `id` (number): 投诉建议ID

### 1.3 按客户查询投诉
- **接口**: `GET /admin/complaints/customer/{customerId}`
- **描述**: 查询指定客户的所有投诉建议
- **参数**: 
  - `customerId` (number): 客户ID
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 1.4 按订单查询投诉
- **接口**: `GET /admin/complaints/order/{orderId}`
- **描述**: 查询指定订单的所有投诉建议
- **参数**: 
  - `orderId` (number): 订单ID
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 1.5 按员工查询投诉
- **接口**: `GET /admin/complaints/employee/{employeeId}`
- **描述**: 查询指定员工的所有投诉建议
- **参数**: 
  - `employeeId` (number): 员工ID
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 1.6 查询投诉统计摘要
- **接口**: `GET /admin/complaints/summary`
- **描述**: 获取投诉建议的统计摘要信息
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 1.7 查询处理历史
- **接口**: `GET /admin/complaints/{id}/history`
- **描述**: 查询指定投诉建议的处理历史记录
- **参数**: `id` (number): 投诉建议ID

### 1.8 查询管理员录入的投诉
- **接口**: `GET /admin/complaints/admin-created`
- **描述**: 查询管理员录入的投诉建议列表
- **参数**: 
  - `page` (number): 页码
  - `pageSize` (number): 每页数量
  - `createdBy` (number): 录入人员ID

## 2. 操作接口

### 2.1 管理员录入投诉建议
- **接口**: `POST /admin/complaints`
- **描述**: 管理端录入投诉建议，可以代表客户录入或录入不关联客户的投诉
- **请求体**: 
  ```json
  {
    "category": "complaint",                    // 必填：complaint/suggestion
    "subCategory": "order",                     // 必填：order/employee/platform/service/workflow
    "title": "订单服务问题",                    // 必填：标题，最大200字符
    "content": "服务质量不满意，希望改进",       // 必填：内容，最大2000字符
    "customerId": 123,                          // 可选：客户ID
    "orderId": 456,                             // 可选：订单ID（订单投诉时必填）
    "employeeId": 789,                          // 可选：员工ID（人员投诉时必填）
    "contactInfo": "13800138000",               // 可选：联系方式，最大100字符
    "photoURLs": ["https://example.com/photo1.jpg"],  // 可选：图片URL数组，最多6张
    "createdBy": 1,                             // 必填：录入人员ID（管理员ID）
    "adminNote": "客户电话反馈"                 // 可选：管理员录入备注，最大500字符
  }
  ```

### 2.2 用户提交投诉建议
- **接口**: `POST /user/complaints`
- **描述**: 用户端提交投诉建议
- **请求体**: 
  ```json
  {
    "category": "complaint",
    "subCategory": "order",
    "title": "订单服务问题",
    "content": "服务质量不满意，希望改进",
    "orderId": 456,                             // 订单投诉时必填
    "employeeId": 789,                          // 人员投诉时必填
    "contactInfo": "13800138000",
    "photoURLs": ["https://example.com/photo1.jpg"]
  }
  ```

### 2.3 更新投诉建议
- **接口**: `PUT /admin/complaints/{id}`
- **描述**: 管理员更新投诉建议信息
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 包含需要更新的字段

### 2.4 处理投诉建议
- **接口**: `POST /admin/complaints/{id}/handle`
- **描述**: 管理员处理投诉建议
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 
  ```json
  {
    "status": "resolved",                       // 新状态
    "result": "已联系客户并解决问题",           // 处理结果
    "handlerId": 1                              // 处理人员ID
  }
  ```

### 2.5 快速更新投诉状态
- **接口**: `PATCH /admin/complaints/{id}/status`
- **描述**: 快速更新投诉建议状态
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 
  ```json
  {
    "status": "processing"                      // 新状态
  }
  ```

### 2.6 删除投诉建议
- **接口**: `DELETE /admin/complaints/{id}`
- **描述**: 删除投诉建议（管理员权限）
- **参数**: `id` (number): 投诉建议ID

## 3. 批量操作接口

### 3.1 批量处理投诉建议
- **接口**: `POST /admin/complaints/batch-handle`
- **描述**: 批量处理多个投诉建议
- **请求体**: 
  ```json
  {
    "ids": [1, 2, 3],                          // 投诉建议ID数组
    "status": "resolved",                       // 新状态
    "result": "批量处理完成",                   // 处理结果
    "handlerId": 1                              // 处理人员ID
  }
  ```

### 3.2 批量删除投诉建议
- **接口**: `DELETE /admin/complaints/batch`
- **描述**: 批量删除多个投诉建议
- **请求体**: 
  ```json
  {
    "ids": [1, 2, 3]                           // 投诉建议ID数组
  }
  ```

## 4. 用户端接口

### 4.1 查询用户投诉列表
- **接口**: `GET /user/complaints`
- **描述**: 用户查询自己的投诉建议列表
- **参数**: 
  - `page` (number): 页码
  - `pageSize` (number): 每页数量
  - `status` (string): 状态筛选

### 4.2 查询用户投诉详情
- **接口**: `GET /user/complaints/{id}`
- **描述**: 用户查询自己的投诉建议详情
- **参数**: `id` (number): 投诉建议ID

### 4.3 用户撤销投诉
- **接口**: `DELETE /user/complaints/{id}`
- **描述**: 用户撤销自己的投诉建议（仅限待处理状态）
- **参数**: `id` (number): 投诉建议ID

## 5. 员工端接口

### 5.1 查询相关投诉
- **接口**: `GET /employee/complaints`
- **描述**: 员工查询与自己相关的投诉建议
- **参数**: 
  - `page` (number): 页码
  - `pageSize` (number): 每页数量
  - `status` (string): 状态筛选

### 5.2 员工回复投诉
- **接口**: `POST /employee/complaints/{id}/reply`
- **描述**: 员工对投诉建议进行回复
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 
  ```json
  {
    "reply": "感谢您的反馈，我们会改进服务质量"
  }
  ```

## 业务规则说明

### 录入规则
- 订单投诉（subCategory=order）时，orderId必填
- 人员投诉（subCategory=employee）时，employeeId必填
- 订单投诉时，系统会自动从订单获取服务人员信息并关联
- customerId可为空，支持录入不关联客户的投诉

### 权限规则
- 管理员拥有最高权限，可以操作任何状态的投诉建议
- 用户只能查看和操作自己的投诉建议
- 员工只能查看与自己相关的投诉建议

### 状态流转
- pending → processing → resolved/closed
- 用户只能撤销待处理状态的投诉
- 管理员可以在任何状态间切换
