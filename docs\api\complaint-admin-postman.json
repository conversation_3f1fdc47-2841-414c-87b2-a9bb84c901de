{"info": {"name": "投诉建议管理API - 管理端", "description": "投诉建议管理模块的管理端API接口集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:7001", "type": "string"}, {"key": "token", "value": "your_admin_token_here", "type": "string"}], "item": [{"name": "查询类接口", "item": [{"name": "查询投诉建议列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/?current=1&pageSize=10&status=pending", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", ""], "query": [{"key": "current", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "status", "value": "pending"}, {"key": "category", "value": "complaint", "disabled": true}, {"key": "subCategory", "value": "order", "disabled": true}, {"key": "keyword", "value": "服务", "disabled": true}, {"key": "startDate", "value": "2024-01-01", "disabled": true}, {"key": "endDate", "value": "2024-12-31", "disabled": true}]}}}, {"name": "查询投诉建议详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/1", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "1"]}}}, {"name": "查询管理员录入的投诉", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/admin-created?current=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "admin-created"], "query": [{"key": "current", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "created<PERSON>y", "value": "1", "disabled": true}]}}}, {"name": "查询指定客户的投诉", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/customer/123/complaints?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "customer", "123", "complaints"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}}, {"name": "查询指定订单的投诉", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/order/456/complaints", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "order", "456", "complaints"]}}}, {"name": "查询指定员工的投诉", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/employee/789/complaints", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "employee", "789", "complaints"]}}}, {"name": "获取投诉统计摘要", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/statistics/summary?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "statistics", "summary"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}, {"key": "category", "value": "complaint", "disabled": true}, {"key": "subCategory", "value": "order", "disabled": true}]}}}, {"name": "查询处理历史", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/1/history", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "1", "history"]}}}]}, {"name": "操作类接口", "item": [{"name": "管理员录入投诉建议", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"category\": \"complaint\",\n  \"subCategory\": \"order\",\n  \"title\": \"订单服务问题\",\n  \"content\": \"客户反馈服务质量不满意，希望改进\",\n  \"customerId\": 123,\n  \"orderId\": 456,\n  \"contactInfo\": \"13800138000\",\n  \"photoURLs\": [\"https://example.com/photo1.jpg\"],\n  \"createdBy\": 1,\n  \"adminNote\": \"客户电话反馈\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", ""]}}}, {"name": "管理员更新投诉建议", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"更新后的标题\",\n  \"content\": \"更新后的内容\",\n  \"adminNote\": \"管理员更新备注\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/1", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "1"]}}}, {"name": "处理投诉建议", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"resolved\",\n  \"result\": \"已联系客户并解决问题\",\n  \"handlerId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/1/handle", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "1", "handle"]}}}, {"name": "更新投诉状态", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"processing\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/1/status", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "1", "status"]}}}, {"name": "删除投诉建议", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/1", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "1"]}}}]}, {"name": "批量操作接口", "item": [{"name": "批量处理投诉建议", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": [1, 2, 3],\n  \"handleData\": {\n    \"status\": \"resolved\",\n    \"result\": \"批量处理完成\",\n    \"handlerId\": 1\n  }\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/batch-handle", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "batch-handle"]}}}, {"name": "批量删除投诉建议", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/batch", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "batch"]}}}]}, {"name": "测试用例", "item": [{"name": "完整流程测试", "item": [{"name": "1. 录入订单投诉", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"category\": \"complaint\",\n  \"subCategory\": \"order\",\n  \"title\": \"测试订单投诉\",\n  \"content\": \"这是一个测试投诉\",\n  \"customerId\": 1,\n  \"orderId\": 1,\n  \"createdBy\": 1,\n  \"adminNote\": \"测试数据\"\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", ""]}}}, {"name": "2. 查询投诉详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/{{complaintId}}", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "{{complaintId}}"]}}}, {"name": "3. 处理投诉", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"resolved\",\n  \"result\": \"测试处理完成\",\n  \"handlerId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/admin/complaints/{{complaintId}}/handle", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "{{complaintId}}", "handle"]}}}, {"name": "4. 查询处理历史", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/admin/complaints/{{complaintId}}/history", "host": ["{{baseUrl}}"], "path": ["admin", "complaints", "{{complaintId}}", "history"]}}}]}]}]}