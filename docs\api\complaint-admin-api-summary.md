# 投诉建议管理API - 接口总览

## 基础信息

**基础路径**: `/admin/complaints`  
**认证方式**: Bearer Token  
**响应格式**: JSON  

## 接口列表

### 查询类接口

| 方法 | 路径 | 功能 | 分页 |
|------|------|------|------|
| GET | `/` | 查询所有投诉建议列表 | ✅ |
| GET | `/:id` | 查询投诉建议详情 | ❌ |
| GET | `/admin-created` | 查询管理员录入的投诉 | ✅ |
| GET | `/customer/:customerId/complaints` | 查询指定客户的投诉 | ✅ |
| GET | `/order/:orderId/complaints` | 查询指定订单的投诉 | ❌ |
| GET | `/employee/:employeeId/complaints` | 查询指定员工的投诉 | ❌ |
| GET | `/statistics/summary` | 获取投诉统计摘要 | ❌ |
| GET | `/:id/history` | 查询处理历史 | ❌ |

### 操作类接口

| 方法 | 路径 | 功能 | 权限要求 |
|------|------|------|----------|
| POST | `/` | 管理员录入投诉建议 | 管理员 |
| PUT | `/:id` | 管理员更新投诉建议 | 管理员 |
| PATCH | `/:id/handle` | 处理投诉建议 | 管理员 |
| PATCH | `/:id/status` | 更新投诉状态 | 管理员 |
| DELETE | `/:id` | 删除投诉建议 | 管理员 |

### 批量操作接口

| 方法 | 路径 | 功能 | 权限要求 |
|------|------|------|----------|
| POST | `/batch-handle` | 批量处理投诉建议 | 管理员 |
| DELETE | `/batch` | 批量删除投诉建议 | 管理员 |

## 核心数据结构

### 投诉建议对象
```typescript
interface Complaint {
  id: number;
  customerId?: number;           // 客户ID（可为空）
  orderId?: number;              // 订单ID
  employeeId?: number;           // 员工ID
  category: 'complaint' | 'suggestion';
  subCategory: 'order' | 'employee' | 'platform' | 'service' | 'workflow';
  title: string;
  content: string;
  contactInfo?: string;
  photoURLs?: string[];
  status: 'pending' | 'processing' | 'resolved' | 'closed';
  result?: string;
  handlerId?: number;
  handledAt?: Date;
  createdBy?: number;            // 管理员录入标识
  adminNote?: string;            // 管理员备注
  createdAt: Date;
  updatedAt: Date;
}
```

## 常用查询参数

### 分页参数
- `current`: 当前页码（默认1）
- `pageSize`: 每页数量（默认10）

### 筛选参数
- `customerId`: 客户ID
- `orderId`: 订单ID
- `employeeId`: 员工ID
- `category`: 大类（complaint/suggestion）
- `subCategory`: 小类（order/employee/platform/service/workflow）
- `status`: 状态（pending/processing/resolved/closed）
- `handlerId`: 处理人员ID
- `createdBy`: 录入人员ID
- `keyword`: 关键词搜索
- `startDate`: 开始日期（YYYY-MM-DD）
- `endDate`: 结束日期（YYYY-MM-DD）

## 快速开始

### 1. 录入投诉建议
```javascript
POST /admin/complaints/
{
  "category": "complaint",
  "subCategory": "order",
  "title": "订单问题",
  "content": "详细描述",
  "customerId": 123,
  "orderId": 456,
  "createdBy": 1,
  "adminNote": "客户电话反馈"
}
```

### 2. 查询投诉列表
```javascript
GET /admin/complaints/?current=1&pageSize=20&status=pending
```

### 3. 处理投诉
```javascript
PATCH /admin/complaints/1/handle
{
  "status": "resolved",
  "result": "问题已解决",
  "handlerId": 1
}
```

### 4. 批量处理
```javascript
POST /admin/complaints/batch-handle
{
  "ids": [1, 2, 3],
  "handleData": {
    "status": "resolved",
    "result": "批量处理完成",
    "handlerId": 1
  }
}
```

## 业务规则

### 必填字段规则
- **订单投诉** (subCategory=order): 必须提供 `orderId`
- **人员投诉** (subCategory=employee): 必须提供 `employeeId`
- **管理员录入**: 必须提供 `createdBy`

### 状态流转
```
pending → processing → resolved/closed
```

### 权限说明
- 管理员可以操作任何状态的投诉建议
- 管理员可以录入不关联客户的投诉
- 订单投诉会自动关联订单的服务人员

## 错误处理

### 常见错误码
- `400`: 参数错误
- `404`: 资源不存在
- `500`: 服务器错误

### 错误响应格式
```json
{
  "errCode": 400,
  "msg": "错误描述",
  "data": null
}
```

## 注意事项

1. 所有日期使用 `YYYY-MM-DD` 格式
2. 图片需要先上传获取URL
3. 批量操作返回详细的成功/失败统计
4. 管理员拥有最高权限，不受状态限制
5. 订单投诉会自动关联服务人员信息

---

**详细文档**: 请参考 [complaint-admin-api.md](./complaint-admin-api.md) 获取完整的接口文档和示例。
